# FCM Troubleshooting Guide

## Common Issues and Solutions

### 1. Notifications Not Appearing

#### Symptoms
- FCM token is generated successfully
- No errors in logs
- Notifications sent from Firebase Console but not received

#### Possible Causes & Solutions

**A. Notification Permissions Not Granted**
```bash
# Check if permission is granted
adb shell dumpsys notification | grep "de.sva.web2app"
```
- **Solution**: Request notification permission in app settings
- **Code Check**: Verify `POST_NOTIFICATIONS` permission in AndroidManifest.xml

**B. Notification Channels Disabled**
```bash
# Check notification channels
adb shell dumpsys notification | grep -A 5 "de.sva.web2app"
```
- **Solution**: Enable notification channels in device settings
- **Code Check**: Verify `NotificationChannelManager.createNotificationChannels()` is called

**C. Battery Optimization**
- **Symptoms**: Notifications work when app is active but not in background
- **Solution**: Add app to battery optimization whitelist
- **Path**: Settings > Battery > Battery Optimization > [App] > Don't optimize

**D. Do Not Disturb Mode**
- **Solution**: Check device Do Not Disturb settings
- **Note**: High priority notifications may still appear

### 2. FCM Token Issues

#### Token Generation Fails

**Symptoms**
```
W/FirebaseMessaging: Fetching FCM registration token failed
```

**Possible Causes & Solutions**

**A. Google Play Services Issues**
```bash
# Check Google Play Services version
adb shell dumpsys package com.google.android.gms | grep versionName
```
- **Solution**: Update Google Play Services
- **Minimum Version**: Check Firebase documentation for requirements

**B. Network Connectivity**
```bash
# Test network connectivity
adb shell ping google.com
```
- **Solution**: Ensure device has internet connection
- **Note**: FCM requires internet for token generation

**C. Firebase Configuration**
- **Check**: `google-services.json` file is present and correct
- **Verify**: Package name matches in Firebase Console
- **Location**: `android/app/google-services.json`

#### Token Refresh Issues

**Symptoms**
```
E/FCMTokenManager: Failed to send refreshed token to server
```

**Solutions**
- Check server endpoint availability
- Verify token format and validation
- Implement retry logic with exponential backoff

### 3. App State Specific Issues

#### Foreground Notifications Not Showing

**Symptoms**
- `onMessageReceived()` is called
- No notification appears on screen

**Causes & Solutions**
- **Missing Notification Display**: Check `sendNotification()` implementation
- **Channel Issues**: Verify notification channel is created
- **Permission Issues**: Ensure notification permission is granted

**Debug Code**
```java
// Add to onMessageReceived()
Log.d("FCM", "Message received in foreground");
Log.d("FCM", "Notification: " + (remoteMessage.getNotification() != null));
```

#### Background Notifications Not Working

**Symptoms**
- Notifications work in foreground
- No notifications when app is in background

**Causes & Solutions**
- **Background Restrictions**: Check if app is restricted
- **Battery Optimization**: Disable for the app
- **Notification Format**: Ensure proper notification payload

**Check Background Restrictions**
```bash
adb shell dumpsys deviceidle | grep "de.sva.web2app"
```

#### Killed State Notifications Not Working

**Symptoms**
- Notifications work in foreground and background
- No notifications when app is force-stopped

**Causes & Solutions**
- **Auto-Start Management**: Some devices prevent auto-start
- **Notification Payload**: Must include notification object, not just data
- **Device Specific**: Check manufacturer-specific settings

### 4. Notification Display Issues

#### Notifications Appear But Don't Open App

**Symptoms**
- Notifications appear in notification tray
- Tapping notification doesn't open app

**Causes & Solutions**
- **PendingIntent Issues**: Check PendingIntent flags
- **Intent Filters**: Verify MainActivity intent filters
- **Activity Launch Mode**: Check `launchMode` in AndroidManifest.xml

**Correct PendingIntent Setup**
```java
PendingIntent pendingIntent = PendingIntent.getActivity(
    context, 
    0, 
    intent,
    PendingIntent.FLAG_IMMUTABLE | PendingIntent.FLAG_UPDATE_CURRENT
);
```

#### Notification Content Not Displaying Correctly

**Symptoms**
- Notifications appear but content is wrong
- Images not loading
- Actions not working

**Solutions**
- **Image URLs**: Verify image URLs are accessible
- **Content Length**: Check for text length limits
- **Action Intents**: Verify action PendingIntents are correct

### 5. Data Payload Issues

#### Data Not Received in App

**Symptoms**
- Notifications appear
- Custom data not available in app

**Causes & Solutions**
- **Data vs Notification**: Understand difference between data and notification payloads
- **Intent Extras**: Check if data is added to intent extras
- **Parsing**: Verify data parsing in `onMessageReceived()`

**Debug Data Reception**
```java
// In onMessageReceived()
for (Map.Entry<String, String> entry : remoteMessage.getData().entrySet()) {
    Log.d("FCM_DATA", entry.getKey() + ": " + entry.getValue());
}
```

### 6. Performance Issues

#### App Becomes Slow After FCM Implementation

**Symptoms**
- App performance degrades
- UI becomes unresponsive

**Causes & Solutions**
- **Main Thread Blocking**: Move heavy operations off main thread
- **Image Loading**: Implement async image loading
- **Memory Leaks**: Check for proper resource cleanup

**Async Image Loading**
```java
// Use background thread for image loading
new Thread(() -> {
    Bitmap bitmap = getBitmapFromUrl(imageUrl);
    runOnUiThread(() -> {
        // Update UI with bitmap
    });
}).start();
```

### 7. Testing Issues

#### Firebase Console Test Messages Not Received

**Symptoms**
- Token is valid
- App is configured correctly
- Test messages from console don't arrive

**Solutions**
- **Token Format**: Ensure token is copied correctly (no extra spaces)
- **App State**: Try testing in different app states
- **Device Time**: Ensure device time is correct
- **Firebase Project**: Verify correct Firebase project is selected

#### Testing Console Shows Errors

**Symptoms**
- Built-in testing console shows errors
- Error count increases

**Debug Steps**
1. Check error details in testing console
2. Review Android logs
3. Verify network connectivity
4. Check Firebase configuration

### 8. Device-Specific Issues

#### Xiaomi/MIUI Devices

**Common Issues**
- Background app restrictions
- Auto-start management
- Battery optimization

**Solutions**
- Add app to auto-start whitelist
- Disable battery optimization
- Set app to "No restrictions" in battery settings

#### Samsung Devices

**Common Issues**
- Adaptive battery
- App power management
- Notification settings

**Solutions**
- Disable adaptive battery for app
- Set app to "Unrestricted" in battery settings
- Check notification categories

#### Huawei Devices

**Common Issues**
- Protected apps
- Battery optimization
- Background app limits

**Solutions**
- Add to protected apps list
- Disable battery optimization
- Enable background activity

### 9. Debugging Tools

#### ADB Commands

```bash
# Check notification settings
adb shell dumpsys notification

# Check battery optimization
adb shell dumpsys deviceidle

# Check app permissions
adb shell dumpsys package de.sva.web2app | grep permission

# Monitor logs
adb logcat | grep -E "(FCM|Firebase|MyFirebaseMsgService)"
```

#### Built-in Debugging

Use the FCM Testing Console:
1. Check error statistics
2. View debug information
3. Test different notification types
4. Verify app state detection

#### Log Analysis

**Key Log Tags to Monitor**
- `MyFirebaseMsgService`
- `FCMTokenManager`
- `FCMErrorHandler`
- `NotificationHelper`
- `AppStateVerifier`

### 10. Production Checklist

Before deploying to production:

- [ ] Test on multiple device types
- [ ] Test with different Android versions
- [ ] Verify battery optimization handling
- [ ] Test notification permissions flow
- [ ] Verify server-side token management
- [ ] Test error handling scenarios
- [ ] Verify notification content guidelines
- [ ] Test deep linking functionality
- [ ] Verify analytics and tracking
- [ ] Test notification scheduling

### 11. Emergency Fixes

#### Quick Fixes for Common Issues

**Notifications Stopped Working Suddenly**
1. Check Firebase Console for service status
2. Verify app is not in battery optimization
3. Restart Google Play Services
4. Clear app cache and restart

**Token Issues**
1. Delete and regenerate token
2. Clear app data and restart
3. Check network connectivity
4. Verify Firebase configuration

**Performance Issues**
1. Move image loading to background thread
2. Implement notification batching
3. Optimize notification content
4. Check for memory leaks

### 12. Getting Help

If issues persist:
1. Check Firebase Status Page
2. Review Firebase Documentation
3. Search Firebase Support Forums
4. Use built-in error reporting
5. Contact Firebase Support (paid plans)

**Useful Resources**
- [Firebase Documentation](https://firebase.google.com/docs/cloud-messaging)
- [Android Notification Guide](https://developer.android.com/guide/topics/ui/notifiers/notifications)
- [Firebase Status Page](https://status.firebase.google.com/)
- [Firebase Support](https://firebase.google.com/support)
