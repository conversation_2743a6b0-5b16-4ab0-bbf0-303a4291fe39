# Firebase Cloud Messaging (FCM) Testing Guide

## Overview

This guide provides comprehensive instructions for testing Firebase Cloud Messaging (FCM) push notifications in your Android application. The implementation includes testing tools and verification utilities to ensure notifications work correctly in all app states.

## Quick Start

### 1. Access the FCM Testing Console

The app includes a built-in testing console that can be accessed in several ways:

- **From MainActivity**: Call `openFCMTestingConsole()` method
- **From Notification**: Send a notification with `custom_action: "open_fcm_testing"`
- **Programmatically**: Start `FCMTestingActivity` directly

### 2. Get Your FCM Token

1. Open the FCM Testing Console
2. The current FCM registration token will be displayed
3. Use the "Copy Token to Clipboard" button to copy it
4. You'll need this token for sending test notifications

## Firebase Console Configuration

### Setting Up Firebase Console for Testing

1. **Access Firebase Console**
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Select your project (`web2app-b3281`)

2. **Navigate to Cloud Messaging**
   - In the left sidebar, click "Cloud Messaging"
   - Click "Send your first message" or "New campaign"

3. **Create Test Message**
   - **Notification title**: Enter a test title (e.g., "Test Notification")
   - **Notification text**: Enter test message (e.g., "This is a test from Firebase Console")
   - **Notification image** (optional): Add image URL for rich notifications

4. **Target Your App**
   - Select "Send test message"
   - Enter your FCM registration token (copied from testing console)
   - Click "Test" to send the notification

### Advanced Messaging Options

#### Data Payload
Add custom data to your notifications:
```json
{
  "action": "open_url",
  "url": "https://example.com",
  "user_id": "12345",
  "priority": "high"
}
```

#### Notification Options
- **Priority**: Set to "High" for important notifications
- **Sound**: Default notification sound
- **Badge**: Show badge count on app icon
- **Click Action**: URL to open when notification is tapped

## Testing Different App States

### 1. Foreground Testing

**Setup:**
- Keep the app open and visible on screen
- Ensure the app is in the foreground

**Test Steps:**
1. Send notification from Firebase Console
2. Notification should appear as heads-up notification
3. Check FCM Testing Console for verification

**Expected Behavior:**
- Notification appears immediately
- App receives message in `onMessageReceived()`
- Heads-up notification displays briefly
- Verification status shows "✓ VERIFIED" for Foreground

### 2. Background Testing

**Setup:**
- Open the app
- Press home button (don't swipe away from recent apps)
- App should be running but not visible

**Test Steps:**
1. Send notification from Firebase Console
2. Notification should appear in notification tray
3. Tap notification to open app
4. Check FCM Testing Console for verification

**Expected Behavior:**
- Notification appears in notification tray
- Tapping opens the app
- App receives notification data
- Verification status shows "✓ VERIFIED" for Background

### 3. Killed State Testing

**Setup:**
- Open the app
- Swipe it away from recent apps OR force stop from Settings
- App should not be running

**Test Steps:**
1. Send notification from Firebase Console
2. Notification should appear in notification tray
3. Tap notification to launch app
4. Check FCM Testing Console for verification

**Expected Behavior:**
- Notification appears even when app is killed
- Tapping launches the app
- App receives notification data on startup
- Verification status shows "✓ VERIFIED" for Killed

## Built-in Testing Features

### FCM Testing Console Features

1. **Token Management**
   - View current FCM token
   - Refresh token
   - Copy token to clipboard

2. **Notification Testing**
   - Send basic test notification
   - Test different notification types
   - Test notification channels
   - Clear all notifications

3. **Advanced Testing**
   - Run all test scenarios
   - Test high/low priority notifications
   - Test notifications with images
   - Test notifications with action buttons

4. **Error Monitoring**
   - View error statistics
   - See last error details
   - Clear error logs

5. **App State Verification**
   - Check verification status for all app states
   - View testing instructions
   - Clear verification data

### Automated Test Suite

Use the "Run All Tests" button to execute:
- Basic notification test
- High priority notification
- Low priority notification
- Action button notification
- Data payload notification
- Long text notification
- Image notification
- Multiple notifications
- Channel-specific notifications

## Troubleshooting

### Common Issues

#### 1. Notifications Not Appearing

**Possible Causes:**
- Notification permissions not granted
- App is in battery optimization
- Notification channels disabled

**Solutions:**
- Check app permissions in device settings
- Disable battery optimization for the app
- Verify notification channels are enabled

#### 2. Token Retrieval Fails

**Possible Causes:**
- Google Play Services not available
- Network connectivity issues
- Firebase configuration problems

**Solutions:**
- Ensure Google Play Services is updated
- Check internet connection
- Verify `google-services.json` is correct

#### 3. Notifications Only Work in Foreground

**Possible Causes:**
- Background app restrictions
- Battery optimization enabled
- Doze mode restrictions

**Solutions:**
- Add app to battery optimization whitelist
- Check background app restrictions
- Test on different devices

### Debug Information

The FCM Testing Console provides debug information:
- Current FCM token
- Token age and validity
- Error count and details
- App state verification status

### Logs

Check Android logs for detailed information:
```bash
adb logcat | grep -E "(FCM|Firebase|MyFirebaseMsgService)"
```

## Testing Checklist

### Pre-Testing Setup
- [ ] Firebase project configured correctly
- [ ] `google-services.json` file in place
- [ ] App has notification permissions
- [ ] FCM token retrieved successfully

### Notification Testing
- [ ] Basic notification works
- [ ] High priority notification works
- [ ] Low priority notification works
- [ ] Notification with image works
- [ ] Notification with actions works
- [ ] Multiple notifications work

### App State Testing
- [ ] Foreground notifications verified
- [ ] Background notifications verified
- [ ] Killed state notifications verified
- [ ] All states show "✓ VERIFIED"

### Advanced Testing
- [ ] Custom data payload handled correctly
- [ ] Notification channels work properly
- [ ] Error handling works correctly
- [ ] Token refresh works

## Production Considerations

### Security
- Never log FCM tokens in production
- Validate all incoming notification data
- Implement proper server-side token management

### Performance
- Handle notifications efficiently
- Avoid blocking the main thread
- Implement proper error handling

### User Experience
- Request notification permissions appropriately
- Provide clear notification content
- Handle notification actions properly

## Support

If you encounter issues:
1. Check the troubleshooting section
2. Review Android logs
3. Use the built-in error reporting
4. Verify Firebase Console configuration

For additional help, refer to the [Firebase Documentation](https://firebase.google.com/docs/cloud-messaging/android/client).
