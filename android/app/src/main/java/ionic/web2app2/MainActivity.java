package de.sva.web2app;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.getcapacitor.BridgeActivity;

public class MainActivity extends BridgeActivity {

    private static final String TAG = "MainActivity";
    private FCMTokenManager fcmTokenManager;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Initialize notification channels
        NotificationChannelManager.createNotificationChannels(this);

        // Request notification permission for Android 13+
        requestNotificationPermission();

        // Initialize FCM token manager
        fcmTokenManager = new FCMTokenManager(this);
        fcmTokenManager.initialize();

        // Handle notification click if app was opened from notification
        handleNotificationClick();

        Log.d(TAG, "MainActivity created with FCM support");
    }

    /**
     * Handle notification click data
     */
    private void handleNotificationClick() {
        Bundle extras = getIntent().getExtras();
        if (extras != null) {
            boolean fromNotification = extras.getBoolean("fcm_notification_clicked", false);
            if (fromNotification) {
                Log.d(TAG, "App opened from notification click");

                // Handle notification data
                for (String key : extras.keySet()) {
                    Object value = extras.get(key);
                    Log.d(TAG, "Notification data - " + key + ": " + value);
                }

                // You can add custom logic here to handle specific notification actions
                String customAction = extras.getString("custom_action");
                if (customAction != null) {
                    handleCustomAction(customAction, extras);
                }
            }
        }
    }

    /**
     * Handle custom actions from notifications
     */
    private void handleCustomAction(String action, Bundle data) {
        Log.d(TAG, "Handling custom action: " + action);

        // Add your custom action handling logic here
        // For example, navigate to specific screens, open URLs, etc.

        // Special action to open FCM testing console
        if ("open_fcm_testing".equals(action)) {
            openFCMTestingConsole();
        }
    }

    /**
     * Open FCM Testing Console
     * This can be called from JavaScript bridge or notification actions
     */
    public void openFCMTestingConsole() {
        Intent intent = new Intent(this, FCMTestingActivity.class);
        startActivity(intent);
        Log.d(TAG, "FCM Testing Console opened");
    }

    /**
     * Get current FCM token (can be called from JavaScript)
     */
    public void getCurrentFCMToken() {
        FCMTokenManager tokenManager = new FCMTokenManager(this);
        tokenManager.getCurrentToken(new FCMTokenManager.TokenCallback() {
            @Override
            public void onTokenReceived(String token) {
                Log.i(TAG, "Current FCM Token: " + token);
                // You can send this token to JavaScript if needed
            }

            @Override
            public void onTokenError(Exception exception) {
                Log.e(TAG, "Error getting FCM token", exception);
            }
        });
    }

    /**
     * Test notification display (for debugging)
     */
    public void testNotificationDisplay() {
        NotificationHelper notificationHelper = new NotificationHelper(this);
        notificationHelper.showSimpleNotification("Test Notification", "This is a test notification from MainActivity");
        Log.d(TAG, "Test notification sent");
    }

    /**
     * Request notification permission for Android 13+
     */
    private void requestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS)
                != PackageManager.PERMISSION_GRANTED) {
                ActivityCompat.requestPermissions(this,
                    new String[]{Manifest.permission.POST_NOTIFICATIONS}, 1001);
                Log.d(TAG, "Requesting notification permission");
            } else {
                Log.d(TAG, "Notification permission already granted");
            }
        } else {
            Log.d(TAG, "Notification permission not required for this Android version");
        }
    }
}
