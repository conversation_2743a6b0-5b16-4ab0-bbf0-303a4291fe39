package de.sva.web2app;

import android.app.Activity;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

/**
 * Testing activity for FCM functionality
 * Provides UI for displaying tokens, testing notifications, and debugging
 */
public class FCMTestingActivity extends Activity {

    private static final String TAG = "FCMTestingActivity";
    
    private TextView tokenTextView;
    private TextView errorStatsTextView;
    private Button refreshTokenButton;
    private Button copyTokenButton;
    private Button testNotificationButton;
    private Button clearErrorsButton;
    
    private FCMTokenManager tokenManager;
    private FCMErrorHandler errorHandler;
    private NotificationHelper notificationHelper;
    private NotificationTester notificationTester;
    private AppStateVerifier appStateVerifier;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Initialize managers
        tokenManager = new FCMTokenManager(this);
        errorHandler = new FCMErrorHandler(this);
        notificationHelper = new NotificationHelper(this);
        notificationTester = new NotificationTester(this);
        appStateVerifier = new AppStateVerifier(this);
        
        // Create simple UI programmatically
        createUI();
        
        // Load initial data
        loadTokenInfo();
        loadErrorStats();
        
        Log.d(TAG, "FCM Testing Activity created");
    }

    /**
     * Create the UI programmatically
     */
    private void createUI() {
        // Create main layout
        android.widget.LinearLayout layout = new android.widget.LinearLayout(this);
        layout.setOrientation(android.widget.LinearLayout.VERTICAL);
        layout.setPadding(32, 32, 32, 32);

        // Title
        TextView titleView = new TextView(this);
        titleView.setText("FCM Testing Console");
        titleView.setTextSize(20);
        titleView.setPadding(0, 0, 0, 32);
        layout.addView(titleView);

        // Token section
        TextView tokenLabel = new TextView(this);
        tokenLabel.setText("FCM Registration Token:");
        tokenLabel.setTextSize(16);
        layout.addView(tokenLabel);

        tokenTextView = new TextView(this);
        tokenTextView.setText("Loading...");
        tokenTextView.setTextSize(12);
        tokenTextView.setPadding(0, 8, 0, 16);
        tokenTextView.setBackgroundColor(0xFFEEEEEE);
        tokenTextView.setPadding(16, 16, 16, 16);
        layout.addView(tokenTextView);

        // Buttons
        refreshTokenButton = new Button(this);
        refreshTokenButton.setText("Refresh Token");
        refreshTokenButton.setOnClickListener(this::onRefreshTokenClick);
        layout.addView(refreshTokenButton);

        copyTokenButton = new Button(this);
        copyTokenButton.setText("Copy Token to Clipboard");
        copyTokenButton.setOnClickListener(this::onCopyTokenClick);
        layout.addView(copyTokenButton);

        testNotificationButton = new Button(this);
        testNotificationButton.setText("Test Notification");
        testNotificationButton.setOnClickListener(this::onTestNotificationClick);
        layout.addView(testNotificationButton);

        // Error stats section
        TextView errorLabel = new TextView(this);
        errorLabel.setText("Error Statistics:");
        errorLabel.setTextSize(16);
        errorLabel.setPadding(0, 32, 0, 8);
        layout.addView(errorLabel);

        errorStatsTextView = new TextView(this);
        errorStatsTextView.setText("Loading...");
        errorStatsTextView.setTextSize(12);
        errorStatsTextView.setPadding(16, 16, 16, 16);
        errorStatsTextView.setBackgroundColor(0xFFFFEEEE);
        layout.addView(errorStatsTextView);

        clearErrorsButton = new Button(this);
        clearErrorsButton.setText("Clear Error Stats");
        clearErrorsButton.setOnClickListener(this::onClearErrorsClick);
        layout.addView(clearErrorsButton);

        // Advanced testing buttons
        Button runAllTestsButton = new Button(this);
        runAllTestsButton.setText("Run All Tests");
        runAllTestsButton.setOnClickListener(this::onRunAllTestsClick);
        layout.addView(runAllTestsButton);

        Button testChannelsButton = new Button(this);
        testChannelsButton.setText("Test Notification Channels");
        testChannelsButton.setOnClickListener(this::onTestChannelsClick);
        layout.addView(testChannelsButton);

        Button clearNotificationsButton = new Button(this);
        clearNotificationsButton.setText("Clear All Notifications");
        clearNotificationsButton.setOnClickListener(this::onClearNotificationsClick);
        layout.addView(clearNotificationsButton);

        // App state verification section
        TextView verificationLabel = new TextView(this);
        verificationLabel.setText("App State Verification:");
        verificationLabel.setTextSize(16);
        verificationLabel.setPadding(0, 32, 0, 8);
        layout.addView(verificationLabel);

        Button showVerificationButton = new Button(this);
        showVerificationButton.setText("Show Verification Status");
        showVerificationButton.setOnClickListener(this::onShowVerificationClick);
        layout.addView(showVerificationButton);

        Button showInstructionsButton = new Button(this);
        showInstructionsButton.setText("Show Testing Instructions");
        showInstructionsButton.setOnClickListener(this::onShowInstructionsClick);
        layout.addView(showInstructionsButton);

        Button clearVerificationButton = new Button(this);
        clearVerificationButton.setText("Clear Verification Data");
        clearVerificationButton.setOnClickListener(this::onClearVerificationClick);
        layout.addView(clearVerificationButton);

        setContentView(layout);
    }

    /**
     * Load and display token information
     */
    private void loadTokenInfo() {
        tokenManager.getCurrentToken(new FCMTokenManager.TokenCallback() {
            @Override
            public void onTokenReceived(String token) {
                runOnUiThread(() -> {
                    tokenTextView.setText(token);
                    Log.d(TAG, "Token loaded: " + token);
                });
            }

            @Override
            public void onTokenError(Exception exception) {
                runOnUiThread(() -> {
                    tokenTextView.setText("Error loading token: " + exception.getMessage());
                    Log.e(TAG, "Error loading token", exception);
                });
            }
        });
    }

    /**
     * Load and display error statistics
     */
    private void loadErrorStats() {
        String errorReport = errorHandler.createErrorReport();
        errorStatsTextView.setText(errorReport);
    }

    /**
     * Handle refresh token button click
     */
    private void onRefreshTokenClick(View view) {
        refreshTokenButton.setEnabled(false);
        refreshTokenButton.setText("Refreshing...");
        
        loadTokenInfo();
        
        // Re-enable button after a delay
        refreshTokenButton.postDelayed(() -> {
            refreshTokenButton.setEnabled(true);
            refreshTokenButton.setText("Refresh Token");
        }, 2000);
    }

    /**
     * Handle copy token button click
     */
    private void onCopyTokenClick(View view) {
        String token = tokenManager.getStoredToken();
        if (token != null) {
            ClipboardManager clipboard = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
            ClipData clip = ClipData.newPlainText("FCM Token", token);
            clipboard.setPrimaryClip(clip);
            Toast.makeText(this, "Token copied to clipboard", Toast.LENGTH_SHORT).show();
            Log.d(TAG, "Token copied to clipboard");
        } else {
            Toast.makeText(this, "No token available", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Handle test notification button click
     */
    private void onTestNotificationClick(View view) {
        notificationTester.testBasicNotification();
        Toast.makeText(this, "Test notification sent", Toast.LENGTH_SHORT).show();
    }

    /**
     * Handle clear errors button click
     */
    private void onClearErrorsClick(View view) {
        errorHandler.clearErrorStats();
        loadErrorStats();
        Toast.makeText(this, "Error statistics cleared", Toast.LENGTH_SHORT).show();
        Log.d(TAG, "Error statistics cleared");
    }

    /**
     * Handle run all tests button click
     */
    private void onRunAllTestsClick(View view) {
        notificationTester.runAllTests();
        Toast.makeText(this, "Running all notification tests", Toast.LENGTH_SHORT).show();
        Log.d(TAG, "Running all notification tests");
    }

    /**
     * Handle test channels button click
     */
    private void onTestChannelsClick(View view) {
        notificationTester.testNotificationChannels();
        Toast.makeText(this, "Testing notification channels", Toast.LENGTH_SHORT).show();
        Log.d(TAG, "Testing notification channels");
    }

    /**
     * Handle clear notifications button click
     */
    private void onClearNotificationsClick(View view) {
        notificationTester.clearAllNotifications();
        Toast.makeText(this, "All notifications cleared", Toast.LENGTH_SHORT).show();
        Log.d(TAG, "All notifications cleared");
    }

    /**
     * Handle show verification button click
     */
    private void onShowVerificationClick(View view) {
        String status = appStateVerifier.getVerificationStatus();
        showInfoDialog("App State Verification", status);
    }

    /**
     * Handle show instructions button click
     */
    private void onShowInstructionsClick(View view) {
        String instructions = appStateVerifier.getTestingInstructions();
        showInfoDialog("Testing Instructions", instructions);
    }

    /**
     * Handle clear verification button click
     */
    private void onClearVerificationClick(View view) {
        appStateVerifier.clearVerificationData();
        Toast.makeText(this, "Verification data cleared", Toast.LENGTH_SHORT).show();
        Log.d(TAG, "Verification data cleared");
    }

    /**
     * Show information dialog
     */
    private void showInfoDialog(String title, String message) {
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
        builder.setTitle(title);
        builder.setMessage(message);
        builder.setPositiveButton("OK", null);

        // Make the dialog scrollable for long content
        android.app.AlertDialog dialog = builder.create();
        dialog.show();

        // Make the message text selectable
        TextView messageView = dialog.findViewById(android.R.id.message);
        if (messageView != null) {
            messageView.setTextIsSelectable(true);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Refresh data when activity resumes
        loadErrorStats();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Cleanup notification tester resources
        if (notificationTester != null) {
            notificationTester.cleanup();
        }
    }

    /**
     * Get debug information for sharing
     */
    public String getDebugInfo() {
        StringBuilder debug = new StringBuilder();
        debug.append("=== FCM Debug Information ===\n");
        debug.append("Token: ").append(tokenManager.getStoredToken()).append("\n");
        debug.append("Token Age: ").append(tokenManager.getTokenAge()).append(" ms\n");
        debug.append("Token Sent to Server: ").append(tokenManager.isTokenSentToServer()).append("\n");
        debug.append("Should Refresh Token: ").append(tokenManager.shouldRefreshToken()).append("\n");
        debug.append("\n");
        debug.append(errorHandler.createErrorReport());
        
        return debug.toString();
    }

    /**
     * Test different notification types
     */
    public void testNotificationTypes() {
        // Test high priority notification
        java.util.Map<String, String> highPriorityData = new java.util.HashMap<>();
        highPriorityData.put("priority", "high");
        notificationHelper.showNotification(
            "High Priority Test",
            "This is a high priority notification",
            highPriorityData,
            NotificationChannelManager.HIGH_PRIORITY_CHANNEL_ID
        );

        // Test low priority notification
        java.util.Map<String, String> lowPriorityData = new java.util.HashMap<>();
        lowPriorityData.put("priority", "low");
        notificationHelper.showNotification(
            "Low Priority Test",
            "This is a low priority notification",
            lowPriorityData,
            NotificationChannelManager.LOW_PRIORITY_CHANNEL_ID
        );
    }

    /**
     * Test topic subscription
     */
    public void testTopicSubscription() {
        tokenManager.subscribeToTopic("test_topic", new FCMTokenManager.TokenOperationCallback() {
            @Override
            public void onSuccess() {
                runOnUiThread(() -> 
                    Toast.makeText(FCMTestingActivity.this, "Subscribed to test topic", Toast.LENGTH_SHORT).show()
                );
            }

            @Override
            public void onError(Exception exception) {
                runOnUiThread(() -> 
                    Toast.makeText(FCMTestingActivity.this, "Failed to subscribe to topic", Toast.LENGTH_SHORT).show()
                );
            }
        });
    }
}
