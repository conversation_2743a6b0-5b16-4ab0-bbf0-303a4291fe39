package de.sva.web2app;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Context;
import android.os.Build;
import android.util.Log;

/**
 * Utility class for managing notification channels
 * Ensures proper notification channel setup for Android 8.0+
 */
public class NotificationChannelManager {

    private static final String TAG = "NotificationChannelMgr";

    // Default channel
    public static final String DEFAULT_CHANNEL_ID = "default_channel_id";
    public static final String DEFAULT_CHANNEL_NAME = "Default Notifications";
    public static final String DEFAULT_CHANNEL_DESCRIPTION = "Default notification channel for the app";

    // High priority channel for important notifications
    public static final String HIGH_PRIORITY_CHANNEL_ID = "high_priority_channel_id";
    public static final String HIGH_PRIORITY_CHANNEL_NAME = "Important Notifications";
    public static final String HIGH_PRIORITY_CHANNEL_DESCRIPTION = "High priority notifications that require immediate attention";

    // Low priority channel for less important notifications
    public static final String LOW_PRIORITY_CHANNEL_ID = "low_priority_channel_id";
    public static final String LOW_PRIORITY_CHANNEL_NAME = "Background Notifications";
    public static final String LOW_PRIORITY_CHANNEL_DESCRIPTION = "Low priority background notifications";

    /**
     * Create all notification channels
     * Should be called when the app starts
     */
    public static void createNotificationChannels(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager notificationManager = 
                (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);

            if (notificationManager != null) {
                // Create default channel
                createDefaultChannel(notificationManager);
                
                // Create high priority channel
                createHighPriorityChannel(notificationManager);
                
                // Create low priority channel
                createLowPriorityChannel(notificationManager);
                
                Log.d(TAG, "All notification channels created successfully");
            } else {
                Log.e(TAG, "NotificationManager is null, cannot create channels");
            }
        } else {
            Log.d(TAG, "Android version < O, notification channels not needed");
        }
    }

    /**
     * Create the default notification channel
     */
    private static void createDefaultChannel(NotificationManager notificationManager) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    DEFAULT_CHANNEL_ID,
                    DEFAULT_CHANNEL_NAME,
                    NotificationManager.IMPORTANCE_DEFAULT
            );
            
            channel.setDescription(DEFAULT_CHANNEL_DESCRIPTION);
            channel.enableLights(true);
            channel.enableVibration(true);
            channel.setShowBadge(true);
            channel.setLockscreenVisibility(NotificationChannel.VISIBILITY_PUBLIC);
            
            notificationManager.createNotificationChannel(channel);
            Log.d(TAG, "Default notification channel created");
        }
    }

    /**
     * Create the high priority notification channel
     */
    private static void createHighPriorityChannel(NotificationManager notificationManager) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    HIGH_PRIORITY_CHANNEL_ID,
                    HIGH_PRIORITY_CHANNEL_NAME,
                    NotificationManager.IMPORTANCE_HIGH
            );
            
            channel.setDescription(HIGH_PRIORITY_CHANNEL_DESCRIPTION);
            channel.enableLights(true);
            channel.enableVibration(true);
            channel.setShowBadge(true);
            channel.setLockscreenVisibility(NotificationChannel.VISIBILITY_PUBLIC);
            
            notificationManager.createNotificationChannel(channel);
            Log.d(TAG, "High priority notification channel created");
        }
    }

    /**
     * Create the low priority notification channel
     */
    private static void createLowPriorityChannel(NotificationManager notificationManager) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    LOW_PRIORITY_CHANNEL_ID,
                    LOW_PRIORITY_CHANNEL_NAME,
                    NotificationManager.IMPORTANCE_LOW
            );
            
            channel.setDescription(LOW_PRIORITY_CHANNEL_DESCRIPTION);
            channel.enableLights(false);
            channel.enableVibration(false);
            channel.setShowBadge(false);
            channel.setLockscreenVisibility(NotificationChannel.VISIBILITY_PUBLIC);
            
            notificationManager.createNotificationChannel(channel);
            Log.d(TAG, "Low priority notification channel created");
        }
    }

    /**
     * Check if a notification channel exists
     */
    public static boolean channelExists(Context context, String channelId) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager notificationManager = 
                (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
            
            if (notificationManager != null) {
                NotificationChannel channel = notificationManager.getNotificationChannel(channelId);
                return channel != null;
            }
        }
        return true; // On older versions, channels don't exist so return true
    }

    /**
     * Delete a notification channel
     */
    public static void deleteChannel(Context context, String channelId) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager notificationManager = 
                (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
            
            if (notificationManager != null) {
                notificationManager.deleteNotificationChannel(channelId);
                Log.d(TAG, "Notification channel deleted: " + channelId);
            }
        }
    }

    /**
     * Get the appropriate channel ID based on priority
     */
    public static String getChannelIdForPriority(String priority) {
        if (priority == null) {
            return DEFAULT_CHANNEL_ID;
        }
        
        switch (priority.toLowerCase()) {
            case "high":
            case "urgent":
            case "important":
                return HIGH_PRIORITY_CHANNEL_ID;
            case "low":
            case "background":
                return LOW_PRIORITY_CHANNEL_ID;
            default:
                return DEFAULT_CHANNEL_ID;
        }
    }
}
