package de.sva.web2app;

import android.content.Context;
import android.util.Log;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Comprehensive notification testing utility
 * Provides various test scenarios for FCM notifications
 */
public class NotificationTester {

    private static final String TAG = "NotificationTester";
    
    private final Context context;
    private final NotificationHelper notificationHelper;
    private final FCMErrorHandler errorHandler;
    private final ScheduledExecutorService scheduler;

    public NotificationTester(Context context) {
        this.context = context;
        this.notificationHelper = new NotificationHelper(context);
        this.errorHandler = new FCMErrorHandler(context);
        this.scheduler = Executors.newScheduledThreadPool(2);
    }

    /**
     * Test basic notification
     */
    public void testBasicNotification() {
        errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Testing basic notification");
        
        Map<String, String> data = new HashMap<>();
        data.put("test_type", "basic");
        data.put("timestamp", String.valueOf(System.currentTimeMillis()));
        
        notificationHelper.showNotification(
            "Basic Test Notification",
            "This is a basic test notification to verify FCM functionality",
            data
        );
    }

    /**
     * Test notification with image
     */
    public void testImageNotification() {
        errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Testing image notification");
        
        Map<String, String> data = new HashMap<>();
        data.put("test_type", "image");
        data.put("image", "https://via.placeholder.com/300x200/0066CC/FFFFFF?text=FCM+Test");
        data.put("timestamp", String.valueOf(System.currentTimeMillis()));
        
        notificationHelper.showNotification(
            "Image Test Notification",
            "This notification includes an image",
            data
        );
    }

    /**
     * Test high priority notification
     */
    public void testHighPriorityNotification() {
        errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Testing high priority notification");
        
        Map<String, String> data = new HashMap<>();
        data.put("test_type", "high_priority");
        data.put("priority", "high");
        data.put("timestamp", String.valueOf(System.currentTimeMillis()));
        
        notificationHelper.showNotification(
            "High Priority Test",
            "This is a high priority notification that should appear prominently",
            data,
            NotificationChannelManager.HIGH_PRIORITY_CHANNEL_ID
        );
    }

    /**
     * Test low priority notification
     */
    public void testLowPriorityNotification() {
        errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Testing low priority notification");
        
        Map<String, String> data = new HashMap<>();
        data.put("test_type", "low_priority");
        data.put("priority", "low");
        data.put("timestamp", String.valueOf(System.currentTimeMillis()));
        
        notificationHelper.showNotification(
            "Low Priority Test",
            "This is a low priority notification",
            data,
            NotificationChannelManager.LOW_PRIORITY_CHANNEL_ID
        );
    }

    /**
     * Test notification with action buttons
     */
    public void testActionNotification() {
        errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Testing action notification");
        
        Map<String, String> data = new HashMap<>();
        data.put("test_type", "action");
        data.put("action_url", "https://firebase.google.com");
        data.put("custom_action", "test_action");
        data.put("custom_action_label", "Test Action");
        data.put("timestamp", String.valueOf(System.currentTimeMillis()));
        
        notificationHelper.showNotification(
            "Action Test Notification",
            "This notification has action buttons",
            data
        );
    }

    /**
     * Test notification with custom data
     */
    public void testDataNotification() {
        errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Testing data notification");
        
        Map<String, String> data = new HashMap<>();
        data.put("test_type", "data");
        data.put("user_id", "12345");
        data.put("message_id", "msg_67890");
        data.put("category", "test");
        data.put("deep_link", "/test/page");
        data.put("timestamp", String.valueOf(System.currentTimeMillis()));
        
        notificationHelper.showNotification(
            "Data Test Notification",
            "This notification contains custom data payload",
            data
        );
    }

    /**
     * Test multiple notifications
     */
    public void testMultipleNotifications() {
        errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Testing multiple notifications");
        
        for (int i = 1; i <= 3; i++) {
            Map<String, String> data = new HashMap<>();
            data.put("test_type", "multiple");
            data.put("notification_number", String.valueOf(i));
            data.put("notification_id", String.valueOf(1000 + i));
            data.put("timestamp", String.valueOf(System.currentTimeMillis()));
            
            notificationHelper.showNotification(
                "Multiple Test #" + i,
                "This is notification number " + i + " of 3",
                data
            );
            
            // Small delay between notifications
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * Test delayed notification
     */
    public void testDelayedNotification(int delaySeconds) {
        errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Testing delayed notification (delay: " + delaySeconds + "s)");
        
        scheduler.schedule(() -> {
            Map<String, String> data = new HashMap<>();
            data.put("test_type", "delayed");
            data.put("delay_seconds", String.valueOf(delaySeconds));
            data.put("timestamp", String.valueOf(System.currentTimeMillis()));
            
            notificationHelper.showNotification(
                "Delayed Test Notification",
                "This notification was delayed by " + delaySeconds + " seconds",
                data
            );
        }, delaySeconds, TimeUnit.SECONDS);
    }

    /**
     * Test notification channel functionality
     */
    public void testNotificationChannels() {
        errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Testing notification channels");
        
        // Test each channel
        String[] channels = {
            NotificationChannelManager.DEFAULT_CHANNEL_ID,
            NotificationChannelManager.HIGH_PRIORITY_CHANNEL_ID,
            NotificationChannelManager.LOW_PRIORITY_CHANNEL_ID
        };
        
        String[] channelNames = {
            "Default Channel",
            "High Priority Channel", 
            "Low Priority Channel"
        };
        
        for (int i = 0; i < channels.length; i++) {
            Map<String, String> data = new HashMap<>();
            data.put("test_type", "channel");
            data.put("channel_id", channels[i]);
            data.put("timestamp", String.valueOf(System.currentTimeMillis()));
            
            notificationHelper.showNotification(
                "Channel Test: " + channelNames[i],
                "Testing notification on " + channelNames[i],
                data,
                channels[i]
            );
            
            // Small delay between notifications
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * Test notification with long text
     */
    public void testLongTextNotification() {
        errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Testing long text notification");
        
        String longText = "This is a very long notification message that should demonstrate " +
                "how the notification system handles extended content. The message should " +
                "be expandable and show the full text when the user expands the notification. " +
                "This helps test the BigTextStyle functionality of Android notifications.";
        
        Map<String, String> data = new HashMap<>();
        data.put("test_type", "long_text");
        data.put("timestamp", String.valueOf(System.currentTimeMillis()));
        
        notificationHelper.showNotification(
            "Long Text Test",
            longText,
            data
        );
    }

    /**
     * Run all notification tests
     */
    public void runAllTests() {
        errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Running all notification tests");
        
        // Run tests with delays to avoid overwhelming the notification system
        scheduler.schedule(this::testBasicNotification, 0, TimeUnit.SECONDS);
        scheduler.schedule(this::testHighPriorityNotification, 2, TimeUnit.SECONDS);
        scheduler.schedule(this::testLowPriorityNotification, 4, TimeUnit.SECONDS);
        scheduler.schedule(this::testActionNotification, 6, TimeUnit.SECONDS);
        scheduler.schedule(this::testDataNotification, 8, TimeUnit.SECONDS);
        scheduler.schedule(this::testLongTextNotification, 10, TimeUnit.SECONDS);
        scheduler.schedule(this::testImageNotification, 12, TimeUnit.SECONDS);
        scheduler.schedule(() -> testDelayedNotification(5), 14, TimeUnit.SECONDS);
        scheduler.schedule(this::testNotificationChannels, 16, TimeUnit.SECONDS);
        scheduler.schedule(this::testMultipleNotifications, 20, TimeUnit.SECONDS);
    }

    /**
     * Clear all notifications
     */
    public void clearAllNotifications() {
        errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Clearing all notifications");
        notificationHelper.cancelAllNotifications();
    }

    /**
     * Test notification error handling
     */
    public void testErrorHandling() {
        errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Testing error handling");
        
        try {
            // Test with null data
            notificationHelper.showNotification("Error Test", "Testing null data", null);
            
            // Test with empty strings
            notificationHelper.showNotification("", "", new HashMap<>());
            
            // Test with very long title
            String longTitle = "This is a very long title that might cause issues ".repeat(10);
            notificationHelper.showNotification(longTitle, "Testing long title", new HashMap<>());
            
        } catch (Exception e) {
            errorHandler.handleFCMError(
                FCMErrorHandler.ErrorType.NOTIFICATION_DISPLAY_FAILED,
                "Error during notification testing",
                e
            );
        }
    }

    /**
     * Cleanup resources
     */
    public void cleanup() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
