package de.sva.web2app;

import android.app.ActivityManager;
import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import java.util.List;

/**
 * Utility class to verify and track app states for FCM testing
 * Helps verify that notifications work in foreground, background, and killed states
 */
public class AppStateVerifier {

    private static final String TAG = "AppStateVerifier";
    private static final String PREFS_NAME = "app_state_prefs";
    private static final String KEY_LAST_FOREGROUND_TIME = "last_foreground_time";
    private static final String KEY_LAST_BACKGROUND_TIME = "last_background_time";
    private static final String KEY_NOTIFICATION_RECEIVED_FOREGROUND = "notif_received_foreground";
    private static final String KEY_NOTIFICATION_RECEIVED_BACKGROUND = "notif_received_background";
    private static final String KEY_NOTIFICATION_RECEIVED_KILLED = "notif_received_killed";

    private final Context context;
    private final SharedPreferences sharedPreferences;
    private final FCMErrorHandler errorHandler;

    public AppStateVerifier(Context context) {
        this.context = context;
        this.sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.errorHandler = new FCMErrorHandler(context);
    }

    /**
     * App state enumeration
     */
    public enum AppState {
        FOREGROUND("Foreground - App is visible and active"),
        BACKGROUND("Background - App is running but not visible"),
        KILLED("Killed - App is not running");

        private final String description;

        AppState(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * Get current app state
     */
    public AppState getCurrentAppState() {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        
        if (activityManager != null) {
            List<ActivityManager.RunningAppProcessInfo> runningProcesses = activityManager.getRunningAppProcesses();
            
            if (runningProcesses != null) {
                String packageName = context.getPackageName();
                
                for (ActivityManager.RunningAppProcessInfo processInfo : runningProcesses) {
                    if (processInfo.processName.equals(packageName)) {
                        switch (processInfo.importance) {
                            case ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND:
                                return AppState.FOREGROUND;
                            case ActivityManager.RunningAppProcessInfo.IMPORTANCE_BACKGROUND:
                            case ActivityManager.RunningAppProcessInfo.IMPORTANCE_SERVICE:
                                return AppState.BACKGROUND;
                            default:
                                return AppState.BACKGROUND;
                        }
                    }
                }
            }
        }
        
        return AppState.KILLED;
    }

    /**
     * Record when app enters foreground
     */
    public void recordForegroundState() {
        long currentTime = System.currentTimeMillis();
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putLong(KEY_LAST_FOREGROUND_TIME, currentTime);
        editor.apply();
        
        errorHandler.log(FCMErrorHandler.LogLevel.DEBUG, "App entered foreground state");
    }

    /**
     * Record when app enters background
     */
    public void recordBackgroundState() {
        long currentTime = System.currentTimeMillis();
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putLong(KEY_LAST_BACKGROUND_TIME, currentTime);
        editor.apply();
        
        errorHandler.log(FCMErrorHandler.LogLevel.DEBUG, "App entered background state");
    }

    /**
     * Record notification received in specific app state
     */
    public void recordNotificationReceived(AppState appState) {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        long currentTime = System.currentTimeMillis();
        
        switch (appState) {
            case FOREGROUND:
                editor.putLong(KEY_NOTIFICATION_RECEIVED_FOREGROUND, currentTime);
                errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Notification received in FOREGROUND state");
                break;
            case BACKGROUND:
                editor.putLong(KEY_NOTIFICATION_RECEIVED_BACKGROUND, currentTime);
                errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Notification received in BACKGROUND state");
                break;
            case KILLED:
                editor.putLong(KEY_NOTIFICATION_RECEIVED_KILLED, currentTime);
                errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Notification received in KILLED state");
                break;
        }
        
        editor.apply();
    }

    /**
     * Check if notifications have been tested in all states
     */
    public boolean areAllStatesVerified() {
        long foregroundTime = sharedPreferences.getLong(KEY_NOTIFICATION_RECEIVED_FOREGROUND, 0);
        long backgroundTime = sharedPreferences.getLong(KEY_NOTIFICATION_RECEIVED_BACKGROUND, 0);
        long killedTime = sharedPreferences.getLong(KEY_NOTIFICATION_RECEIVED_KILLED, 0);
        
        return foregroundTime > 0 && backgroundTime > 0 && killedTime > 0;
    }

    /**
     * Get verification status for each app state
     */
    public String getVerificationStatus() {
        StringBuilder status = new StringBuilder();
        status.append("=== App State Verification Status ===\n");
        
        long foregroundTime = sharedPreferences.getLong(KEY_NOTIFICATION_RECEIVED_FOREGROUND, 0);
        long backgroundTime = sharedPreferences.getLong(KEY_NOTIFICATION_RECEIVED_BACKGROUND, 0);
        long killedTime = sharedPreferences.getLong(KEY_NOTIFICATION_RECEIVED_KILLED, 0);
        
        status.append("Foreground: ").append(foregroundTime > 0 ? "✓ VERIFIED" : "✗ NOT TESTED").append("\n");
        if (foregroundTime > 0) {
            status.append("  Last tested: ").append(new java.util.Date(foregroundTime)).append("\n");
        }
        
        status.append("Background: ").append(backgroundTime > 0 ? "✓ VERIFIED" : "✗ NOT TESTED").append("\n");
        if (backgroundTime > 0) {
            status.append("  Last tested: ").append(new java.util.Date(backgroundTime)).append("\n");
        }
        
        status.append("Killed: ").append(killedTime > 0 ? "✓ VERIFIED" : "✗ NOT TESTED").append("\n");
        if (killedTime > 0) {
            status.append("  Last tested: ").append(new java.util.Date(killedTime)).append("\n");
        }
        
        status.append("\nOverall Status: ").append(areAllStatesVerified() ? "✓ ALL VERIFIED" : "⚠ INCOMPLETE").append("\n");
        status.append("Current State: ").append(getCurrentAppState().getDescription()).append("\n");
        
        return status.toString();
    }

    /**
     * Clear verification data
     */
    public void clearVerificationData() {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.remove(KEY_NOTIFICATION_RECEIVED_FOREGROUND);
        editor.remove(KEY_NOTIFICATION_RECEIVED_BACKGROUND);
        editor.remove(KEY_NOTIFICATION_RECEIVED_KILLED);
        editor.apply();
        
        errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Verification data cleared");
    }

    /**
     * Get testing instructions for each app state
     */
    public String getTestingInstructions() {
        StringBuilder instructions = new StringBuilder();
        instructions.append("=== FCM App State Testing Instructions ===\n\n");
        
        instructions.append("1. FOREGROUND TESTING:\n");
        instructions.append("   - Keep the app open and visible\n");
        instructions.append("   - Send a test notification from Firebase Console\n");
        instructions.append("   - Notification should appear as a heads-up notification\n");
        instructions.append("   - App should receive the message in onMessageReceived()\n\n");
        
        instructions.append("2. BACKGROUND TESTING:\n");
        instructions.append("   - Open the app, then press home button (don't swipe away)\n");
        instructions.append("   - App should be in background but still running\n");
        instructions.append("   - Send a test notification from Firebase Console\n");
        instructions.append("   - Notification should appear in notification tray\n");
        instructions.append("   - Tapping notification should open the app\n\n");
        
        instructions.append("3. KILLED STATE TESTING:\n");
        instructions.append("   - Open the app, then swipe it away from recent apps\n");
        instructions.append("   - Or force stop the app from Settings > Apps\n");
        instructions.append("   - Send a test notification from Firebase Console\n");
        instructions.append("   - Notification should still appear in notification tray\n");
        instructions.append("   - Tapping notification should launch the app\n\n");
        
        instructions.append("FIREBASE CONSOLE TESTING:\n");
        instructions.append("1. Go to Firebase Console > Cloud Messaging\n");
        instructions.append("2. Click 'Send your first message'\n");
        instructions.append("3. Enter notification title and text\n");
        instructions.append("4. Select your app as target\n");
        instructions.append("5. Send the message\n\n");
        
        instructions.append("VERIFICATION:\n");
        instructions.append("- Check this testing console after each test\n");
        instructions.append("- Verify that notifications are recorded for each state\n");
        instructions.append("- All three states should show '✓ VERIFIED' when complete\n");
        
        return instructions.toString();
    }

    /**
     * Generate test report
     */
    public String generateTestReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== FCM Testing Report ===\n");
        report.append("Generated: ").append(new java.util.Date()).append("\n\n");
        
        report.append(getVerificationStatus()).append("\n");
        
        if (areAllStatesVerified()) {
            report.append("🎉 CONGRATULATIONS! 🎉\n");
            report.append("FCM notifications are working correctly in all app states.\n");
            report.append("Your implementation is ready for production.\n");
        } else {
            report.append("⚠️ TESTING INCOMPLETE ⚠️\n");
            report.append("Please complete testing for all app states.\n");
            report.append("Refer to testing instructions for guidance.\n");
        }
        
        return report.toString();
    }

    /**
     * Simulate app state change for testing
     */
    public void simulateAppStateChange(AppState newState) {
        errorHandler.log(FCMErrorHandler.LogLevel.DEBUG, "Simulating app state change to: " + newState);
        
        switch (newState) {
            case FOREGROUND:
                recordForegroundState();
                break;
            case BACKGROUND:
                recordBackgroundState();
                break;
            case KILLED:
                // Can't really simulate killed state, but we can log it
                errorHandler.log(FCMErrorHandler.LogLevel.DEBUG, "Cannot simulate killed state - app would need to be actually killed");
                break;
        }
    }
}
