package de.sva.web2app;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;
import android.util.Log;

import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;
import androidx.core.content.ContextCompat;

/**
 * Robust notification manager that handles all edge cases and ensures notifications always display
 */
public class RobustNotificationManager {

    private static final String TAG = "RobustNotificationMgr";
    private static final String CHANNEL_ID = "fcm_robust_channel";
    private static final String CHANNEL_NAME = "FCM Notifications";
    
    private final Context context;
    private final NotificationManager notificationManager;
    private final FCMErrorHandler errorHandler;

    public RobustNotificationManager(Context context) {
        this.context = context;
        this.notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        this.errorHandler = new FCMErrorHandler(context);
        
        // Ensure notification channel exists
        createNotificationChannel();
    }

    /**
     * Display notification with maximum compatibility and reliability
     */
    public void showNotification(String title, String body) {
        try {
            errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Attempting to show robust notification");
            
            // Check notification permission
            if (!areNotificationsEnabled()) {
                errorHandler.log(FCMErrorHandler.LogLevel.WARNING, "Notifications are disabled");
                return;
            }
            
            // Create notification channel (safe to call multiple times)
            createNotificationChannel();
            
            // Create intent for notification click
            Intent intent = new Intent(context, MainActivity.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
            intent.putExtra("from_notification", true);
            intent.putExtra("notification_time", System.currentTimeMillis());
            
            PendingIntent pendingIntent = PendingIntent.getActivity(
                context,
                (int) System.currentTimeMillis(),
                intent,
                PendingIntent.FLAG_IMMUTABLE | PendingIntent.FLAG_UPDATE_CURRENT
            );
            
            // Get default notification sound
            Uri defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
            
            // Build notification with maximum compatibility
            NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(android.R.drawable.ic_dialog_info) // Use system icon for maximum compatibility
                .setContentTitle(title != null && !title.isEmpty() ? title : "New Message")
                .setContentText(body != null && !body.isEmpty() ? body : "You have a new message")
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setCategory(NotificationCompat.CATEGORY_MESSAGE)
                .setAutoCancel(true)
                .setShowWhen(true)
                .setWhen(System.currentTimeMillis())
                .setContentIntent(pendingIntent)
                .setSound(defaultSoundUri)
                .setVibrate(new long[]{0, 250, 250, 250})
                .setLights(0xFF0000FF, 300, 1000)
                .setOnlyAlertOnce(false)
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC);
            
            // Add big text style for longer messages
            if (body != null && body.length() > 40) {
                builder.setStyle(new NotificationCompat.BigTextStyle().bigText(body));
            }
            
            // Set importance for Android 8.0+
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                builder.setChannelId(CHANNEL_ID);
            }
            
            // Generate unique notification ID
            int notificationId = (int) (System.currentTimeMillis() % Integer.MAX_VALUE);
            
            // Show notification using both methods for maximum compatibility
            try {
                notificationManager.notify(notificationId, builder.build());
                errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Notification displayed via NotificationManager with ID: " + notificationId);
            } catch (Exception e) {
                errorHandler.log(FCMErrorHandler.LogLevel.WARNING, "NotificationManager failed, trying NotificationManagerCompat", e);
                
                // Fallback to NotificationManagerCompat
                NotificationManagerCompat notificationManagerCompat = NotificationManagerCompat.from(context);
                notificationManagerCompat.notify(notificationId, builder.build());
                errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Notification displayed via NotificationManagerCompat with ID: " + notificationId);
            }
            
            // Log success
            errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Robust notification successfully displayed");
            
        } catch (Exception e) {
            errorHandler.log(FCMErrorHandler.LogLevel.ERROR, "Failed to show robust notification", e);
            
            // Last resort: try with minimal notification
            showMinimalNotification(title, body);
        }
    }
    
    /**
     * Create notification channel for Android 8.0+
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try {
                NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    CHANNEL_NAME,
                    NotificationManager.IMPORTANCE_HIGH
                );
                
                channel.setDescription("Notifications from Firebase Cloud Messaging");
                channel.enableLights(true);
                channel.setLightColor(0xFF0000FF);
                channel.enableVibration(true);
                channel.setVibrationPattern(new long[]{0, 250, 250, 250});
                channel.setShowBadge(true);
                channel.setLockscreenVisibility(NotificationCompat.VISIBILITY_PUBLIC);
                channel.setBypassDnd(false);
                
                notificationManager.createNotificationChannel(channel);
                errorHandler.log(FCMErrorHandler.LogLevel.DEBUG, "Notification channel created: " + CHANNEL_ID);
                
            } catch (Exception e) {
                errorHandler.log(FCMErrorHandler.LogLevel.ERROR, "Failed to create notification channel", e);
            }
        }
    }
    
    /**
     * Check if notifications are enabled
     */
    private boolean areNotificationsEnabled() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // Android 13+ requires explicit permission
                boolean hasPermission = ContextCompat.checkSelfPermission(context, 
                    android.Manifest.permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED;
                errorHandler.log(FCMErrorHandler.LogLevel.DEBUG, "POST_NOTIFICATIONS permission: " + hasPermission);
                return hasPermission;
            } else {
                // Pre-Android 13
                NotificationManagerCompat notificationManager = NotificationManagerCompat.from(context);
                boolean enabled = notificationManager.areNotificationsEnabled();
                errorHandler.log(FCMErrorHandler.LogLevel.DEBUG, "Notifications enabled: " + enabled);
                return enabled;
            }
        } catch (Exception e) {
            errorHandler.log(FCMErrorHandler.LogLevel.WARNING, "Could not check notification permission", e);
            return true; // Assume enabled if we can't check
        }
    }
    
    /**
     * Minimal notification as last resort
     */
    private void showMinimalNotification(String title, String body) {
        try {
            errorHandler.log(FCMErrorHandler.LogLevel.WARNING, "Attempting minimal notification as fallback");
            
            NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setContentTitle(title != null ? title : "Message")
                .setContentText(body != null ? body : "New message")
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setAutoCancel(true);
            
            int notificationId = (int) (System.currentTimeMillis() % 1000);
            notificationManager.notify(notificationId, builder.build());
            
            errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Minimal notification displayed with ID: " + notificationId);
            
        } catch (Exception e) {
            errorHandler.log(FCMErrorHandler.LogLevel.ERROR, "Even minimal notification failed", e);
        }
    }
    
    /**
     * Test notification to verify the system works
     */
    public void showTestNotification() {
        showNotification("Test Notification", "This is a test to verify notifications are working properly");
    }
    
    /**
     * Get notification status for debugging
     */
    public String getNotificationStatus() {
        StringBuilder status = new StringBuilder();
        status.append("=== Notification Status ===\n");
        
        try {
            status.append("Android Version: ").append(Build.VERSION.SDK_INT).append("\n");
            status.append("Notifications Enabled: ").append(areNotificationsEnabled()).append("\n");
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                NotificationChannel channel = notificationManager.getNotificationChannel(CHANNEL_ID);
                if (channel != null) {
                    status.append("Channel Exists: true\n");
                    status.append("Channel Importance: ").append(channel.getImportance()).append("\n");
                    status.append("Channel Enabled: ").append(channel.getImportance() != NotificationManager.IMPORTANCE_NONE).append("\n");
                } else {
                    status.append("Channel Exists: false\n");
                }
            }
            
        } catch (Exception e) {
            status.append("Error getting status: ").append(e.getMessage()).append("\n");
        }
        
        return status.toString();
    }
    
    /**
     * Clear all notifications
     */
    public void clearAllNotifications() {
        try {
            notificationManager.cancelAll();
            errorHandler.log(FCMErrorHandler.LogLevel.INFO, "All notifications cleared");
        } catch (Exception e) {
            errorHandler.log(FCMErrorHandler.LogLevel.ERROR, "Failed to clear notifications", e);
        }
    }
}
