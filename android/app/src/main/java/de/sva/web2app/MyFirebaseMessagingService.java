package de.sva.web2app;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;

/**
 * Firebase Cloud Messaging Service for handling push notifications
 * Handles both foreground and background message reception
 */
public class MyFirebaseMessagingService extends FirebaseMessagingService {

    private static final String TAG = "MyFirebaseMsgService";
    private static final String CHANNEL_ID = "default_channel_id";
    private static final String CHANNEL_NAME = "Default Notifications";
    private static final String CHANNEL_DESCRIPTION = "Default notification channel for the app";

    private FCMErrorHandler errorHandler;

    @Override
    public void onCreate() {
        super.onCreate();
        errorHandler = new FCMErrorHandler(this);
        createNotificationChannel();
        errorHandler.log(FCMErrorHandler.LogLevel.INFO, "FirebaseMessagingService created");
    }

    /**
     * Called when message is received.
     *
     * @param remoteMessage Object representing the message received from Firebase Cloud Messaging.
     */
    @Override
    public void onMessageReceived(RemoteMessage remoteMessage) {
        try {
            errorHandler.logOperationStart("Message received from: " + remoteMessage.getFrom());

            // Record notification received for app state verification
            AppStateVerifier appStateVerifier = new AppStateVerifier(this);
            AppStateVerifier.AppState currentState = appStateVerifier.getCurrentAppState();
            appStateVerifier.recordNotificationReceived(currentState);

            errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Notification received in app state: " + currentState);

            // Check if message contains a data payload.
            if (remoteMessage.getData().size() > 0) {
                errorHandler.log(FCMErrorHandler.LogLevel.DEBUG, "Message data payload: " + remoteMessage.getData());

                // Handle data payload
                handleDataPayload(remoteMessage.getData());
            }

            // Check if message contains a notification payload.
            if (remoteMessage.getNotification() != null) {
                errorHandler.log(FCMErrorHandler.LogLevel.DEBUG, "Message Notification Body: " + remoteMessage.getNotification().getBody());

                // Handle notification payload
                sendNotification(
                    remoteMessage.getNotification().getTitle(),
                    remoteMessage.getNotification().getBody(),
                    remoteMessage.getData()
                );
            }

            errorHandler.logOperationSuccess("Message processing completed");

        } catch (Exception e) {
            errorHandler.handleFCMError(
                FCMErrorHandler.ErrorType.MESSAGE_PROCESSING_FAILED,
                "Failed to process received message",
                e
            );
        }
    }

    /**
     * Called if the FCM registration token is updated. This may occur if the security of
     * the previous token had been compromised. Note that this is called when the
     * FCM registration token is initially generated so this is where you would retrieve the token.
     */
    @Override
    public void onNewToken(String token) {
        try {
            errorHandler.logOperationStart("Token refresh");
            errorHandler.log(FCMErrorHandler.LogLevel.INFO, "Refreshed token: " + token);

            // Use FCMTokenManager to handle token refresh
            FCMTokenManager tokenManager = new FCMTokenManager(this);
            tokenManager.onTokenRefresh(token);

            errorHandler.logOperationSuccess("Token refresh completed");

        } catch (Exception e) {
            errorHandler.handleFCMError(
                FCMErrorHandler.ErrorType.TOKEN_REFRESH_FAILED,
                "Failed to handle token refresh",
                e
            );
        }
    }

    /**
     * Handle data payload of FCM message
     */
    private void handleDataPayload(java.util.Map<String, String> data) {
        Log.d(TAG, "Handling data payload");
        
        // Extract custom data
        String title = data.get("title");
        String body = data.get("body");
        String action = data.get("action");
        String url = data.get("url");
        
        // If we have title and body in data, show notification
        if (title != null && body != null) {
            sendNotification(title, body, data);
        }
        
        // Handle custom actions
        if (action != null) {
            handleCustomAction(action, data);
        }
    }

    /**
     * Handle custom actions from FCM data payload
     */
    private void handleCustomAction(String action, java.util.Map<String, String> data) {
        Log.d(TAG, "Handling custom action: " + action);
        
        switch (action) {
            case "open_url":
                String url = data.get("url");
                if (url != null) {
                    Log.d(TAG, "Action: open URL - " + url);
                    // Handle URL opening logic here
                }
                break;
            case "navigate_to":
                String route = data.get("route");
                if (route != null) {
                    Log.d(TAG, "Action: navigate to - " + route);
                    // Handle navigation logic here
                }
                break;
            default:
                Log.d(TAG, "Unknown action: " + action);
                break;
        }
    }



    /**
     * Create notification channel for Android 8.0+
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    CHANNEL_NAME,
                    NotificationManager.IMPORTANCE_DEFAULT
            );
            channel.setDescription(CHANNEL_DESCRIPTION);
            channel.enableLights(true);
            channel.enableVibration(true);
            channel.setShowBadge(true);

            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(channel);
                Log.d(TAG, "Notification channel created: " + CHANNEL_ID);
            }
        }
    }

    /**
     * Send notification using the NotificationHelper
     */
    private void sendNotification(String title, String messageBody, java.util.Map<String, String> data) {
        try {
            errorHandler.logOperationStart("Displaying notification");
            NotificationHelper notificationHelper = new NotificationHelper(this);
            notificationHelper.showNotificationBasedOnAppState(title, messageBody, data);
            errorHandler.logOperationSuccess("Notification displayed");
        } catch (Exception e) {
            errorHandler.handleFCMError(
                FCMErrorHandler.ErrorType.NOTIFICATION_DISPLAY_FAILED,
                "Failed to display notification",
                e
            );
        }
    }
}
