package de.sva.web2app;

import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;

/**
 * Helper class for creating and displaying notifications
 * Handles different notification types and app states
 */
public class NotificationHelper {

    private static final String TAG = "NotificationHelper";
    private final Context context;
    private final NotificationManager notificationManager;

    public NotificationHelper(Context context) {
        this.context = context;
        this.notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
    }

    /**
     * Display a notification with title, body and optional data
     */
    public void showNotification(String title, String body, Map<String, String> data) {
        showNotification(title, body, data, NotificationChannelManager.DEFAULT_CHANNEL_ID);
    }

    /**
     * Simple notification for testing - always shows
     */
    public void showSimpleNotification(String title, String body) {
        try {
            // Ensure notification channels exist
            NotificationChannelManager.createNotificationChannels(context);

            // Create simple notification that always shows
            NotificationCompat.Builder builder = new NotificationCompat.Builder(context, NotificationChannelManager.DEFAULT_CHANNEL_ID)
                .setSmallIcon(android.R.drawable.ic_dialog_info) // Use system icon to avoid resource issues
                .setContentTitle(title != null ? title : "Test Notification")
                .setContentText(body != null ? body : "Test message")
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setDefaults(NotificationCompat.DEFAULT_ALL)
                .setAutoCancel(true)
                .setOnlyAlertOnce(false);

            int notificationId = (int) System.currentTimeMillis();
            notificationManager.notify(notificationId, builder.build());

            Log.d(TAG, "Simple notification displayed with ID: " + notificationId);

        } catch (Exception e) {
            Log.e(TAG, "Error showing simple notification", e);
        }
    }

    /**
     * Display a notification with specified channel
     */
    public void showNotification(String title, String body, Map<String, String> data, String channelId) {
        try {
            // Create intent for when notification is tapped
            Intent intent = createNotificationIntent(data);
            PendingIntent pendingIntent = PendingIntent.getActivity(
                context, 
                0, 
                intent,
                PendingIntent.FLAG_IMMUTABLE | PendingIntent.FLAG_UPDATE_CURRENT
            );

            // Build the notification
            NotificationCompat.Builder builder = createNotificationBuilder(
                title, 
                body, 
                channelId, 
                pendingIntent
            );

            // Add image if provided
            String imageUrl = data != null ? data.get("image") : null;
            if (imageUrl != null && !imageUrl.isEmpty()) {
                addImageToNotification(builder, imageUrl);
            }

            // Add action buttons if provided
            addActionButtons(builder, data);

            // Show the notification
            int notificationId = generateNotificationId(data);
            notificationManager.notify(notificationId, builder.build());
            
            Log.d(TAG, "Notification displayed with ID: " + notificationId);
            
        } catch (Exception e) {
            Log.e(TAG, "Error showing notification", e);
        }
    }

    /**
     * Create the base notification builder
     */
    private NotificationCompat.Builder createNotificationBuilder(String title, String body, 
                                                                String channelId, PendingIntent pendingIntent) {
        Uri defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
        
        return new NotificationCompat.Builder(context, channelId)
                .setSmallIcon(R.mipmap.ic_launcher)
                .setContentTitle(title != null ? title : "New Message")
                .setContentText(body != null ? body : "You have a new message")
                .setAutoCancel(true)
                .setSound(defaultSoundUri)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                .setDefaults(NotificationCompat.DEFAULT_ALL)
                .setStyle(new NotificationCompat.BigTextStyle().bigText(body))
                .setCategory(NotificationCompat.CATEGORY_MESSAGE)
                .setOnlyAlertOnce(false);
    }

    /**
     * Create intent for notification tap
     */
    private Intent createNotificationIntent(Map<String, String> data) {
        Intent intent = new Intent(context, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
        
        // Add FCM data to intent
        if (data != null) {
            for (Map.Entry<String, String> entry : data.entrySet()) {
                intent.putExtra(entry.getKey(), entry.getValue());
            }
            intent.putExtra("fcm_notification_clicked", true);
        }
        
        return intent;
    }

    /**
     * Add image to notification if URL is provided
     */
    private void addImageToNotification(NotificationCompat.Builder builder, String imageUrl) {
        try {
            Bitmap bitmap = getBitmapFromUrl(imageUrl);
            if (bitmap != null) {
                builder.setLargeIcon(bitmap)
                       .setStyle(new NotificationCompat.BigPictureStyle()
                               .bigPicture(bitmap)
                               .bigLargeIcon((Bitmap) null));
                Log.d(TAG, "Image added to notification");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error loading notification image", e);
        }
    }

    /**
     * Download bitmap from URL
     */
    private Bitmap getBitmapFromUrl(String imageUrl) {
        try {
            URL url = new URL(imageUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setDoInput(true);
            connection.connect();
            InputStream input = connection.getInputStream();
            return BitmapFactory.decodeStream(input);
        } catch (IOException e) {
            Log.e(TAG, "Error downloading image", e);
            return null;
        }
    }

    /**
     * Add action buttons to notification
     */
    private void addActionButtons(NotificationCompat.Builder builder, Map<String, String> data) {
        if (data == null) return;

        // Add "Open" action
        String actionUrl = data.get("action_url");
        if (actionUrl != null && !actionUrl.isEmpty()) {
            Intent actionIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(actionUrl));
            PendingIntent actionPendingIntent = PendingIntent.getActivity(
                context, 
                1, 
                actionIntent,
                PendingIntent.FLAG_IMMUTABLE
            );
            builder.addAction(R.mipmap.ic_launcher, "Open", actionPendingIntent);
        }

        // Add custom action if provided
        String customAction = data.get("custom_action");
        String customActionLabel = data.get("custom_action_label");
        if (customAction != null && customActionLabel != null) {
            Intent customIntent = new Intent(context, MainActivity.class);
            customIntent.putExtra("custom_action", customAction);
            customIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            
            PendingIntent customPendingIntent = PendingIntent.getActivity(
                context, 
                2, 
                customIntent,
                PendingIntent.FLAG_IMMUTABLE | PendingIntent.FLAG_UPDATE_CURRENT
            );
            builder.addAction(R.mipmap.ic_launcher, customActionLabel, customPendingIntent);
        }
    }

    /**
     * Generate unique notification ID
     */
    private int generateNotificationId(Map<String, String> data) {
        if (data != null && data.containsKey("notification_id")) {
            try {
                return Integer.parseInt(data.get("notification_id"));
            } catch (NumberFormatException e) {
                Log.w(TAG, "Invalid notification_id format, using timestamp");
            }
        }
        return (int) System.currentTimeMillis();
    }

    /**
     * Cancel a specific notification
     */
    public void cancelNotification(int notificationId) {
        notificationManager.cancel(notificationId);
        Log.d(TAG, "Notification cancelled: " + notificationId);
    }

    /**
     * Cancel all notifications
     */
    public void cancelAllNotifications() {
        notificationManager.cancelAll();
        Log.d(TAG, "All notifications cancelled");
    }

    /**
     * Check if app is in foreground
     */
    public static boolean isAppInForeground(Context context) {
        // This is a simplified check - in a real app you might want to use
        // ActivityLifecycleCallbacks or a similar mechanism for more accurate detection
        return true; // For now, always show notifications
    }

    /**
     * Show notification based on app state
     */
    public void showNotificationBasedOnAppState(String title, String body, Map<String, String> data) {
        if (isAppInForeground(context)) {
            Log.d(TAG, "App is in foreground, showing notification");
            // In foreground - show notification normally
            showNotification(title, body, data);
        } else {
            Log.d(TAG, "App is in background, showing notification");
            // In background - show notification normally (FCM handles this automatically)
            showNotification(title, body, data);
        }
    }
}
