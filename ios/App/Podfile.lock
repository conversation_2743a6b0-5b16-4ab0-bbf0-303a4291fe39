PODS:
  - Capacitor (7.4.2):
    - Capac<PERSON><PERSON><PERSON>ova
  - CapacitorApp (7.0.1):
    - Capacitor
  - CapacitorCommunityPrivacyScreen (6.0.0):
    - Capacitor
  - CapacitorCordova (7.4.2)
  - CapacitorHaptics (7.0.1):
    - Capacitor
  - CapacitorKeyboard (7.0.1):
    - Capacitor
  - CapacitorNetwork (7.0.1):
    - Capacitor
  - CapacitorPreferences (7.0.1):
    - Capacitor
  - CapacitorSplashScreen (7.0.1):
    - Capacitor
  - CapacitorStatusBar (7.0.1):
    - Capacitor
  - CapgoInappbrowser (7.12.1):
    - Capacitor

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../node_modules/@capacitor/app`)"
  - "CapacitorCommunityPrivacyScreen (from `../../node_modules/@capacitor-community/privacy-screen`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorHaptics (from `../../node_modules/@capacitor/haptics`)"
  - "CapacitorKeyboard (from `../../node_modules/@capacitor/keyboard`)"
  - "CapacitorNetwork (from `../../node_modules/@capacitor/network`)"
  - "CapacitorPreferences (from `../../node_modules/@capacitor/preferences`)"
  - "CapacitorSplashScreen (from `../../node_modules/@capacitor/splash-screen`)"
  - "CapacitorStatusBar (from `../../node_modules/@capacitor/status-bar`)"
  - "CapgoInappbrowser (from `../../node_modules/@capgo/inappbrowser`)"

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../node_modules/@capacitor/app"
  CapacitorCommunityPrivacyScreen:
    :path: "../../node_modules/@capacitor-community/privacy-screen"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorHaptics:
    :path: "../../node_modules/@capacitor/haptics"
  CapacitorKeyboard:
    :path: "../../node_modules/@capacitor/keyboard"
  CapacitorNetwork:
    :path: "../../node_modules/@capacitor/network"
  CapacitorPreferences:
    :path: "../../node_modules/@capacitor/preferences"
  CapacitorSplashScreen:
    :path: "../../node_modules/@capacitor/splash-screen"
  CapacitorStatusBar:
    :path: "../../node_modules/@capacitor/status-bar"
  CapgoInappbrowser:
    :path: "../../node_modules/@capgo/inappbrowser"

SPEC CHECKSUMS:
  Capacitor: 156d98aba46ec01dde72c4bbb92fa626837cad29
  CapacitorApp: d63334c052278caf5d81585d80b21905c6f93f39
  CapacitorCommunityPrivacyScreen: 088ffe903fe97c6de54f31407d1bc72c2528a5fc
  CapacitorCordova: 5e58d04631bc5094894ac106e2bf1da18a9e6151
  CapacitorHaptics: 70e47470fa1a6bd6338cd102552e3846b7f9a1b3
  CapacitorKeyboard: 969647d0ca2e5c737d7300088e2517aa832434e2
  CapacitorNetwork: 07ec4c69c1bb696f41c23e00d31bda1bbb221bba
  CapacitorPreferences: cbf154e5e5519b7f5ab33817a334dda1e98387f9
  CapacitorSplashScreen: 19cd3573e57507e02d6f34597a8c421e00931487
  CapacitorStatusBar: 275cbf2f4dfc00388f519ef80c7ec22edda342c9
  CapgoInappbrowser: f4b704a3deac42079bfdbc1794bb4cd137f57bce

PODFILE CHECKSUM: 7534dedcbcef30a9d2f09b00a290a81a1563e637

COCOAPODS: 1.16.2
